#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复持仓记录格式脚本
- 将价格格式化为3位小数
- 将买入时间更新为包含日期的完整格式（上两个交易日的15:00）
- 格式化手续费和金额的小数位数
"""

import json
import time
from datetime import datetime, timedelta
from xtquant import xtdata

def get_last_trading_day_time(days_back=2):
    """获取指定天数前的交易日的15:00时间字符串"""
    try:
        today = datetime.now().strftime('%Y%m%d')

        # 获取当前年份的交易日历
        current_year = int(today[:4])
        start_date = f"{current_year}0101"
        end_date = f"{current_year}1231"

        # 修正API调用，使用命名参数
        trading_dates = xtdata.get_trading_dates('SH', start_time=start_date, end_time=end_date)

        if trading_dates is not None and len(trading_dates) > 0:
            # 处理不同的返回格式
            trading_dates_str = []
            for d in trading_dates:
                if isinstance(d, str) and len(d) == 8:
                    # 已经是YYYYMMDD格式的字符串
                    trading_dates_str.append(d)
                elif hasattr(d, 'strftime'):
                    # datetime对象
                    trading_dates_str.append(d.strftime('%Y%m%d'))
                elif isinstance(d, (int, float)):
                    # 时间戳格式，转换为日期
                    try:
                        # 转换毫秒时间戳为日期字符串
                        date_str = time.strftime('%Y%m%d', time.localtime(d / 1000))
                        trading_dates_str.append(date_str)
                    except:
                        # 如果不是时间戳，可能是日期整数格式（如20250802）
                        date_str = str(int(d))
                        if len(date_str) == 8:
                            trading_dates_str.append(date_str)
                else:
                    # 其他格式，尝试转换为字符串
                    date_str = str(d)
                    if len(date_str) == 8 and date_str.isdigit():
                        trading_dates_str.append(date_str)

            # 排序交易日期
            trading_dates_str.sort()

            # 找到今天之前的交易日
            past_trading_days = []
            for date_str in reversed(trading_dates_str):
                if date_str < today:
                    past_trading_days.append(date_str)
                    if len(past_trading_days) >= days_back:
                        break

            if len(past_trading_days) >= days_back:
                # 获取指定天数前的交易日
                target_date = past_trading_days[days_back - 1]
                # 格式化为 YYYY-MM-DD 15:00:00
                formatted_date = f"{target_date[:4]}-{target_date[4:6]}-{target_date[6:8]} 15:00:00"
                return formatted_date
            else:
                # 如果交易日不够，使用最早的交易日
                if past_trading_days:
                    target_date = past_trading_days[-1]
                    formatted_date = f"{target_date[:4]}-{target_date[4:6]}-{target_date[6:8]} 15:00:00"
                    return formatted_date
                else:
                    # 如果没有找到交易日，返回默认时间
                    yesterday = datetime.now() - timedelta(days=2)
                    return yesterday.strftime('%Y-%m-%d 15:00:00')
        else:
            # 如果无法获取交易日历，返回默认时间
            yesterday = datetime.now() - timedelta(days=2)
            return yesterday.strftime('%Y-%m-%d 15:00:00')

    except Exception:
        # 出错时返回默认时间
        yesterday = datetime.now() - timedelta(days=2)
        return yesterday.strftime('%Y-%m-%d 15:00:00')

def fix_position_records():
    """修复持仓记录格式"""
    try:
        # 读取现有的持仓记录
        with open('ETF交易系统持仓记录.json', 'r', encoding='utf-8') as f:
            position_records = json.load(f)
        
        print("开始修复持仓记录格式...")
        
        # 获取上两个交易日的15:00时间
        default_buy_time = get_last_trading_day_time(days_back=2)
        print(f"默认买入时间设置为: {default_buy_time}")
        
        fixed_count = 0
        
        for code, position_info in position_records.items():
            if 'buy_queue' in position_info:
                for buy_record in position_info['buy_queue']:
                    # 修复价格格式（保留3位小数）
                    if 'buy_price' in buy_record:
                        old_price = buy_record['buy_price']
                        buy_record['buy_price'] = round(float(old_price), 3)
                    
                    # 修复买入时间格式
                    if 'buy_time' in buy_record:
                        old_time = buy_record['buy_time']
                        # 如果是只有时分秒的格式，替换为完整日期时间
                        if len(old_time) <= 8 and ':' in old_time:
                            buy_record['buy_time'] = default_buy_time
                            print(f"  {code}: 时间 {old_time} -> {default_buy_time}")
                    
                    # 修复手续费格式（保留5位小数）
                    if 'fee' in buy_record:
                        buy_record['fee'] = round(float(buy_record['fee']), 5)
                    
                    # 修复实际金额格式（保留2位小数）
                    if 'actual_amount' in buy_record:
                        buy_record['actual_amount'] = round(float(buy_record['actual_amount']), 2)
                
                # 修复总计数据格式
                if 'total_cost' in position_info:
                    position_info['total_cost'] = round(float(position_info['total_cost']), 2)
                
                if 'total_fee' in position_info:
                    position_info['total_fee'] = round(float(position_info['total_fee']), 5)
                
                fixed_count += 1
        
        # 备份原文件
        backup_filename = f'ETF交易系统持仓记录_备份_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(position_records, f, ensure_ascii=False, indent=2)
        print(f"原文件已备份为: {backup_filename}")
        
        # 保存修复后的文件
        with open('ETF交易系统持仓记录.json', 'w', encoding='utf-8') as f:
            json.dump(position_records, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 持仓记录格式修复完成！共修复 {fixed_count} 个持仓记录")
        print("修复内容:")
        print("  - 价格格式化为3位小数")
        print("  - 买入时间更新为完整日期时间格式")
        print("  - 手续费格式化为5位小数")
        print("  - 金额格式化为2位小数")
        
    except FileNotFoundError:
        print("❌ 未找到持仓记录文件: ETF交易系统持仓记录.json")
    except Exception as e:
        print(f"❌ 修复持仓记录时出错: {str(e)}")

if __name__ == "__main__":
    fix_position_records()
